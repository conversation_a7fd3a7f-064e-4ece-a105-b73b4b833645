import { NextResponse } from 'next/server';
import { query } from '@/lib/db';

export async function GET() {
  try {
    console.log('🔍 [Debug] Starting package debug...');

    // 1. Check all packages in the table
    const allPackages = await query(`
      SELECT package_id, provider_id, name, is_active 
      FROM service_packages
    `) as any[];
    
    console.log('🔍 [Debug] All packages:', allPackages);

    // 2. Check specific package for provider 4
    const provider4Packages = await query(`
      SELECT package_id, provider_id, name, is_active 
      FROM service_packages 
      WHERE provider_id = 4
    `) as any[];
    
    console.log('🔍 [Debug] Provider 4 packages:', provider4Packages);

    // 3. Check with different is_active conditions
    const activePackages1 = await query(`
      SELECT COUNT(*) as count 
      FROM service_packages 
      WHERE provider_id = 4 AND is_active = 1
    `) as any[];
    
    console.log('🔍 [Debug] Count with is_active = 1:', activePackages1);

    const activePackagesTrue = await query(`
      SELECT COUNT(*) as count 
      FROM service_packages 
      WHERE provider_id = 4 AND is_active = TRUE
    `) as any[];
    
    console.log('🔍 [Debug] Count with is_active = TRUE:', activePackagesTrue);

    const activePackagesAll = await query(`
      SELECT COUNT(*) as count 
      FROM service_packages 
      WHERE provider_id = 4
    `) as any[];
    
    console.log('🔍 [Debug] Count without is_active filter:', activePackagesAll);

    // 4. Check data types
    const tableInfo = await query(`
      DESCRIBE service_packages
    `) as any[];
    
    console.log('🔍 [Debug] Table structure:', tableInfo);

    // 5. If table is empty, insert the missing package data
    if (allPackages.length === 0) {
      console.log('🔍 [Debug] Table is empty, inserting missing package data...');

      try {
        const insertResult = await query(`
          INSERT INTO service_packages
          (package_id, provider_id, name, description, category, cremation_type, processing_time, price, delivery_fee_per_km, conditions, is_active, created_at, updated_at)
          VALUES
          (9, 4, 'asdadd', 'asdasdasdasd', 'Private', 'Standard', '1-2 days', 3231.00, 0.00, 'asdasd', 1, '2025-05-28 06:40:57', '2025-05-28 06:40:57')
        `);

        console.log('🔍 [Debug] Insert result:', insertResult);

        // Re-fetch the data after insert
        const newAllPackages = await query(`
          SELECT package_id, provider_id, name, is_active
          FROM service_packages
        `) as any[];

        console.log('🔍 [Debug] After insert - All packages:', newAllPackages);

        return NextResponse.json({
          message: 'Package data inserted successfully',
          allPackages: newAllPackages,
          provider4Packages,
          counts: {
            withIsActive1: activePackages1[0]?.count || 0,
            withIsActiveTrue: activePackagesTrue[0]?.count || 0,
            withoutFilter: activePackagesAll[0]?.count || 0
          },
          tableInfo,
          insertResult
        });

      } catch (insertError) {
        console.error('🔍 [Debug] Insert error:', insertError);
        return NextResponse.json({
          error: 'Failed to insert package data',
          details: (insertError as Error).message,
          allPackages,
          provider4Packages,
          counts: {
            withIsActive1: activePackages1[0]?.count || 0,
            withIsActiveTrue: activePackagesTrue[0]?.count || 0,
            withoutFilter: activePackagesAll[0]?.count || 0
          },
          tableInfo
        });
      }
    }

    return NextResponse.json({
      allPackages,
      provider4Packages,
      counts: {
        withIsActive1: activePackages1[0]?.count || 0,
        withIsActiveTrue: activePackagesTrue[0]?.count || 0,
        withoutFilter: activePackagesAll[0]?.count || 0
      },
      tableInfo
    });

  } catch (error) {
    console.error('🔍 [Debug] Error:', error);
    return NextResponse.json({ error: 'Debug failed', details: (error as Error).message }, { status: 500 });
  }
}
